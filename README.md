
# 🎧 reCAPTCHA Audio Solver ⚡

[![Versi](https://img.shields.io/badge/versi-3.8.1-blue.svg)](https://github.com/Moryata/Recaptcha-Audio)
[![Lisensi](https://img.shields.io/badge/lisensi-Open%20Source-green.svg)](LICENSE.md)
[![Performance](https://img.shields.io/badge/performance-optimized-brightgreen.svg)](OPTIMIZATION_SUMMARY.md)

**Otomatis selesaikan reCAPTCHA audio di Chrome dengan performa optimal, UI modern dan multi-bahasa!**

## ✨ Fitur Utama

- ⚡ **Performa Optimal**: Injeksi instant, deteksi pintar dengan MutationObserver
- 🤖 **Otomatis**: Deteksi, klik, proses audio, dan isi jawaban secara otomatis
- 🔄 **Server Redundan**: Menggunakan beberapa server untuk pemrosesan audio yang lebih andal
- 🌙 **Mode Gelap**: Tam<PERSON><PERSON> nyaman untuk penggunaan malam hari
- 🈳 **Multi-Bahasa**: Dukungan bahasa EN/ID/ES
- 📈 **Statistik**: Pantau jumlah captcha berhasil/gagal secara real-time
- 🛡️ **Privasi**: Tidak mengumpulkan data pribadi, komunikasi terenkripsi

## 🚀 Instalasi Cepat

1. **Clone/unduh** repo ini
   ```bash
   git clone https://github.com/moryata/Recaptcha-Audio.git
   ```
2. Buka Chrome → `chrome://extensions/` → Aktifkan **Developer mode**
3. Klik **Load unpacked** → Pilih folder hasil clone
4. Ekstensi siap digunakan!

## 💡 Cara Pakai

1. Klik ikon ekstensi di toolbar Chrome
2. Aktifkan toggle untuk mulai otomatisasi
3. **[BARU]** Pilih bahasa dan mode tampilan sesuai preferensi
4. Buka halaman dengan reCAPTCHA
5. Ekstensi akan otomatis menyelesaikan captcha audio dengan performa optimal
6. **[BARU]** Pantau statistik performa di popup

## ⚠️ Troubleshooting

- **Bot terdeteksi**: Klik tombol "Coba Lagi" atau tunggu beberapa saat
- **Audio gagal diproses**: Server mungkin sibuk, ekstensi akan mencoba server alternatif
- **Server tidak merespon**: Ekstensi akan otomatis beralih ke server cadangan
- **Solver tidak berjalan**: Pastikan toggle aktif dan refresh halaman
- **Gagal berulang kali**: Setelah 5 kali percobaan, akan otomatis beralih ke mode gambar

## 🔄 Versi Terbaru (3.8.1)

- ⚡ **Performa Ditingkatkan**: Polling interval dioptimalkan menjadi 300ms untuk kecepatan dan stabilitas lebih baik
- 🔒 **Pembatasan URL**: Ekstensi hanya aktif pada URL yang mengandung "/recaptcha/"
- 🔄 **Fallback Otomatis**: Beralih ke verifikasi gambar setelah 5x percobaan audio gagal
- 🛡️ **Error Handling Lebih Baik**: Penanganan error yang lebih robust di semua komponen
- 🎨 **UI Modern**: Desain dengan animasi dan efek visual yang lebih baik
- ⚠️ **Deteksi Bot**: Tampilan pesan bot detection dengan tombol "Coba Lagi"
- ✨ **Animasi Status**: Ikon dan animasi untuk setiap kondisi (aktif/nonaktif)
- 📱 **Responsif**: Tampilan optimal di berbagai ukuran layar

## 👨‍💻 Kredit & Lisensi

Dikembangkan oleh **Muhammad Surya Pratama (Moryata)**
Open source, silakan gunakan sesuai lisensi di LICENSE.md