'use strict';

/**
 * reCAPTCHA Audio Solver v3.8.2
 * Background script - optimized version
 */

// Initialize extension state
chrome.runtime.onInstalled.addListener(() => {
  try {
    chrome.storage.local.set({
      enabled: true,
      solveCount: 0,
      failCount: 0,
      lastStatus: "Active",
      botDetected: false,
      lastUpdated: Date.now()
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('[reCAPTCHA Audio Solver] Installation storage error:', chrome.runtime.lastError.message);
      } else {
        console.log('reCAPTCHA Audio Solver v3.8.2 extension installed');
      }
    });

    // Set badge color
    chrome.action.setBadgeBackgroundColor({ color: '#4CAF50' });
  } catch (error) {
    console.error('[reCAPTCHA Audio Solver] Installation error:', error.message);
  }
});

// Optimized cookie deletion function with better error handling and performance
async function deleteCookiesForRecaptcha() {
  console.log('[reCAPTCHA Audio Solver] Deleting cookies for recaptcha.net domain');

  try {
    // Check if cookies API is available
    if (!chrome.cookies || !chrome.cookies.getAll || !chrome.cookies.remove) {
      console.warn('[reCAPTCHA Audio Solver] Cookies API not available');
      return { success: false, error: 'Cookies API not available' };
    }

    // Optimized domain list - only target necessary domains
    const domains = [
      '.recaptcha.net',
      'www.recaptcha.net',
      '.google.com',
      'www.google.com'
    ];

    // Get all cookies in parallel with timeout protection
    const cookiePromises = domains.map(domain =>
      Promise.race([
        chrome.cookies.getAll({ domain }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
      ]).catch(error => {
        console.warn(`[reCAPTCHA Audio Solver] Failed to get cookies for ${domain}:`, error.message);
        return [];
      })
    );

    const cookieArrays = await Promise.all(cookiePromises);
    const [recaptchaCookies, wwwRecaptchaCookies, googleCookies, wwwGoogleCookies] = cookieArrays;

    // More efficient filtering for Google cookies with validation
    const relevantGoogleCookies = [...(googleCookies || []), ...(wwwGoogleCookies || [])].filter(
      cookie => {
        if (!cookie || !cookie.name || !cookie.path) return false;
        const name = cookie.name.toLowerCase();
        const path = cookie.path.toLowerCase();
        return name.includes('captcha') || path.includes('recaptcha');
      }
    );

    // Combine all cookies with validation
    const allCookies = [
      ...(recaptchaCookies || []),
      ...(wwwRecaptchaCookies || []),
      ...relevantGoogleCookies
    ].filter(cookie => cookie && cookie.name && cookie.domain);

    if (allCookies.length === 0) {
      return { success: true, count: 0 };
    }

    // Optimized deletion with better batching and error handling
    const batchSize = Math.min(allCookies.length <= 5 ? 1 : 6, 10); // Smaller batches for better reliability
    const deletionPromises = [];
    let deletedCount = 0;

    for (let i = 0; i < allCookies.length; i += batchSize) {
      const batch = allCookies.slice(i, i + batchSize);
      const batchPromise = Promise.all(batch.map(async cookie => {
        try {
          const domain = cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain;
          const url = `https://${domain}${cookie.path || '/'}`;

          await chrome.cookies.remove({
            url: url,
            name: cookie.name
          });
          deletedCount++;
          return true;
        } catch (error) {
          console.warn(`[reCAPTCHA Audio Solver] Failed to delete cookie ${cookie.name}:`, error.message);
          return false;
        }
      }));
      deletionPromises.push(batchPromise);
    }

    await Promise.all(deletionPromises);

    return { success: true, count: deletedCount };
  } catch (error) {
    console.error('[reCAPTCHA Audio Solver] Error deleting cookies:', error);
    return { success: false, error: error.message };
  }
}

// Process audio from server - optimized version with centralized constants
async function processAudioFromServer(audioUrl, serverUrl) {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  console.log(`${logPrefix} Processing audio...`);

  try {
    // Validate inputs
    if (!audioUrl || !serverUrl || typeof audioUrl !== 'string' || typeof serverUrl !== 'string') {
      return {
        success: false,
        error: 'Invalid input parameters',
        errorType: 'ValidationError'
      };
    }

    // Validate URLs
    try {
      new URL(audioUrl);
      new URL(serverUrl);
    } catch (urlError) {
      return {
        success: false,
        error: 'Invalid URL format',
        errorType: 'ValidationError'
      };
    }

    // Import user agents from constants - avoid duplication
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ];

    // Get a random user agent
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

    // Request headers - defined once
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'text/plain',
      'User-Agent': userAgent,
      'Origin': chrome.runtime.getURL(''),
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    };

    // Make the request to the audio processing server with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    const response = await fetch(serverUrl, {
      method: 'POST',
      headers,
      body: 'input=' + encodeURIComponent(audioUrl) + '&lang=en',
      credentials: 'omit',
      mode: 'cors',
      cache: 'no-store',
      redirect: 'follow',
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    // Handle non-OK responses
    if (!response.ok) {
      const errorMsg = `Server error: ${response.status} ${response.statusText}`;
      console.error(`${logPrefix} ${errorMsg}`);
      return {
        success: false,
        error: errorMsg,
        status: response.status,
        errorType: 'ServerError'
      };
    }

    const text = await response.text();

    // Enhanced validation with better checks
    if (!text || typeof text !== 'string') {
      console.error(`${logPrefix} Empty or invalid response`);
      return {
        success: false,
        error: 'Empty response from server',
        errorType: 'ValidationError'
      };
    }

    // Clean the text
    const cleanText = text.trim();

    // Combined validation check with improved logic
    const hasInvalidChars = /<|>|&lt;|&gt;/.test(cleanText);
    const isTooLong = cleanText.length > 50;
    const isTooShort = cleanText.length < 2 && !/^\d$/.test(cleanText);
    const isOnlyWhitespace = /^\s*$/.test(cleanText);

    if (hasInvalidChars || isTooLong || isTooShort || isOnlyWhitespace) {
      console.error(`${logPrefix} Invalid response format`);
      return {
        success: false,
        error: 'Invalid response format from server',
        errorType: 'ValidationError'
      };
    }

    // Log the successful answer - this is important to keep
    console.log(`${logPrefix} Successfully processed audio: ${cleanText}`);
    return {
      success: true,
      text: cleanText
    };

  } catch (error) {
    // Handle different types of errors
    if (error.name === 'AbortError') {
      console.error(`${logPrefix} Request timeout`);
      return {
        success: false,
        error: 'Request timeout',
        errorType: 'Timeout'
      };
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      console.error(`${logPrefix} Network error`);
      return {
        success: false,
        error: 'Network error',
        errorType: 'NetworkError'
      };
    }

    // Keep error logs but simplify them
    console.error(`${logPrefix} Error processing audio: ${error.message || 'Unknown error'}`);
    return {
      success: false,
      error: error.message || 'Unknown error',
      errorType: error.name || 'Error'
    };
  }
}

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  // Using _sender to indicate we're aware of the parameter but not using it

  // Handle status updates
  if (request.action === "updateStatus") {
    if (request.stats) {
      // Enhanced badge display with success/failure ratio
      const solved = Math.max(0, request.stats.solved || 0);
      const failed = Math.max(0, request.stats.failed || 0);
      const total = solved + failed;

      // Use provided success rate if available, otherwise calculate
      let successRate = 0;
      if (request.stats.successRate !== undefined) {
        successRate = Math.max(0, Math.min(100, request.stats.successRate));
      } else if (total > 0) {
        successRate = Math.round((solved / total) * 100);
      }

      // Update badge with improved logic
      if (total > 0) {
        // Show success count with success rate indicator
        chrome.action.setBadgeText({ text: `${solved}` });

        // Dynamic badge color based on success rate with improved thresholds
        let badgeColor;
        if (!request.stats.enabled) {
          badgeColor = '#EA4335'; // Red when disabled
        } else if (successRate >= 85) {
          badgeColor = '#4CAF50'; // Green for high success rate
        } else if (successRate >= 70) {
          badgeColor = '#FF9800'; // Orange for medium success rate
        } else {
          badgeColor = '#F44336'; // Red for low success rate
        }
        chrome.action.setBadgeBackgroundColor({ color: badgeColor });
      } else {
        // No attempts yet
        chrome.action.setBadgeText({ text: '' });
        const badgeColor = request.stats.enabled ? '#4CAF50' : '#EA4335';
        chrome.action.setBadgeBackgroundColor({ color: badgeColor });
      }

      // Update storage with latest stats and trigger real-time updates
      try {
        const updateData = {
          solveCount: solved,
          failCount: failed,
          lastStatus: request.status || 'Active',
          lastUpdated: Date.now(),
          successRate: successRate,
          enabled: request.stats.enabled !== undefined ? request.stats.enabled : true
        };

        chrome.storage.local.set(updateData, () => {
          if (chrome.runtime.lastError) {
            console.error('[reCAPTCHA Audio Solver] Storage error:', chrome.runtime.lastError.message);
          } else {
            // Broadcast update to all tabs for real-time sync
            chrome.tabs.query({}, (tabs) => {
              if (tabs && tabs.length > 0) {
                tabs.forEach(tab => {
                  if (tab && tab.id) {
                    chrome.tabs.sendMessage(tab.id, {
                      action: 'statsUpdated',
                      stats: updateData
                    }).catch(() => {
                      // Ignore errors for tabs without content script
                    });
                  }
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('[reCAPTCHA Audio Solver] Failed to update storage:', error.message);
      }
    }
    return false;
  }

  // Helper function to safely send response
  const safelySendResponse = (response) => {
    try {
      if (!chrome.runtime.lastError) {
        sendResponse(response);
      } else {
        // Only log critical errors
        if (chrome.runtime.lastError.message !== "The message port closed before a response was received.") {
          console.warn(`[reCAPTCHA Audio Solver] Message port closed: ${chrome.runtime.lastError.message}`);
        }
      }
    } catch (error) {
      // Only log actual errors, not expected conditions
      console.error(`[reCAPTCHA Audio Solver] Error sending response: ${error.message}`);
    }
  };

  // Handle delete cookies request
  if (request.action === "deleteCookies") {
    (async () => {
      try {
        const result = await deleteCookiesForRecaptcha();
        safelySendResponse(result);
      } catch (error) {
        safelySendResponse({
          success: false,
          error: error.message || 'Unknown error'
        });
      }
    })();

    return true; // Indicates that sendResponse will be called asynchronously
  }

  // Process audio request
  if (request.action === "processAudio") {
    // Enhanced input validation for security
    if (!request.audioUrl || !request.serverUrl ||
        typeof request.audioUrl !== 'string' ||
        typeof request.serverUrl !== 'string') {
      sendResponse({
        success: false,
        error: 'Invalid or missing required parameters'
      });
      return false;
    }

    // Validate URL format for security
    try {
      new URL(request.audioUrl);
      new URL(request.serverUrl);
    } catch (error) {
      sendResponse({
        success: false,
        error: 'Invalid URL format provided'
      });
      return false;
    }

    // Process the audio
    (async () => {
      try {
        const result = await processAudioFromServer(request.audioUrl, request.serverUrl);
        safelySendResponse(result);
      } catch (error) {
        safelySendResponse({
          success: false,
          error: error.message || 'Unknown error'
        });
      }
    })();

    return true; // Indicates that sendResponse will be called asynchronously
  }

  return false; // For other unhandled requests
});
