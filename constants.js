'use strict';

/**
 * reCAPTCHA Audio Solver - Constants and Configuration
 * Centralized configuration management for better maintainability
 */

// Version information - single source of truth
const VERSION_INFO = {
  VERSION: '3.8.2',
  BUILD_DATE: '2025-07-28',
  AUTHOR: '<PERSON> (Moryata)'
};

// Performance and timing constants
const PERFORMANCE_CONFIG = {
  POLLING_INTERVAL_MS: 500,
  CACHE_DURATION_MS: 2000,
  MESSAGE_TIMEOUT_MS: 15000,
  DEBOUNCE_STATUS_UPDATE_MS: 200,
  MUTATION_OBSERVER_DEBOUNCE_MS: 50,
  FETCH_TIMEOUT_MS: 12000
};

// reCAPTCHA attempt limits
const ATTEMPT_LIMITS = {
  MAX_ATTEMPTS_PER_SERVER: 3,
  MAX_TOTAL_ATTEMPTS: 5,
  COOKIE_BATCH_SIZE: 8,
  SMALL_COOKIE_THRESHOLD: 5
};

// DOM selectors for reCAPTCHA elements
const SELECTORS = {
  CHECKBOX: '.recaptcha-checkbox-border',
  AUDIO_BUTTON: '#recaptcha-audio-button',
  IMAGE_BUTTON: '#recaptcha-image-button',
  IMAGE_SELECT: '#rc-imageselect',
  AUDIO_SOURCE: '#audio-source',
  RESPONSE_FIELD: '.rc-audiochallenge-response-field',
  AUDIO_RESPONSE: '#audio-response',
  AUDIO_ERROR_MESSAGE: '.rc-audiochallenge-error-message',
  RELOAD_BUTTON: '#recaptcha-reload-button',
  DOSCAPTCHA: '.rc-doscaptcha-body',
  VERIFY_BUTTON: '#recaptcha-verify-button'
};

// Server endpoints for audio processing
const SERVERS = [
  'https://engageub.pythonanywhere.com',
  'https://engageub1.pythonanywhere.com'
];

// Cookie domains for cleanup
const COOKIE_DOMAINS = [
  '.recaptcha.net',
  'www.recaptcha.net',
  '.google.com',
  'www.google.com'
];

// User agents for requests
const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36'
];

// Export constants for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = {
    VERSION_INFO,
    PERFORMANCE_CONFIG,
    ATTEMPT_LIMITS,
    SELECTORS,
    SERVERS,
    COOKIE_DOMAINS,
    USER_AGENTS
  };
} else {
  // Browser environment - attach to window
  window.RECAPTCHA_CONSTANTS = {
    VERSION_INFO,
    PERFORMANCE_CONFIG,
    ATTEMPT_LIMITS,
    SELECTORS,
    SERVERS,
    COOKIE_DOMAINS,
    USER_AGENTS
  };
}
