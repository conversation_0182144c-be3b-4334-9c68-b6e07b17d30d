<!DOCTYPE html>
<html>
<head>
  <title>SVG to PNG Converter</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .icon-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 20px 0;
    }
    canvas {
      border: 1px solid #ccc;
      margin: 10px 0;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    .instructions {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }
    #status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
    }
    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .error {
      background-color: #f2dede;
      color: #a94442;
    }
    .manual-save {
      margin-top: 10px;
      font-style: italic;
      color: #666;
    }
  </style>
</head>
<body>
  <h1>Icon Converter for reCAPTCHA Audio Solver</h1>

  <div class="instructions">
    <h2>Instructions:</h2>
    <ol>
      <li>Click the "Generate Icons" button below</li>
      <li>Click on each "Download" button to save the icons</li>
      <li>If automatic download doesn't work, right-click on the image and select "Save Image As..."</li>
      <li>Save each icon with the appropriate filename (icon16.png, icon48.png, icon128.png)</li>
      <li>Place the saved PNG files in the icons folder of your extension</li>
    </ol>
  </div>

  <button id="generateBtn">Generate Icons</button>
  <div id="status"></div>

  <div id="iconContainer"></div>

  <script>
    const statusDiv = document.getElementById('status');
    const iconContainer = document.getElementById('iconContainer');
    const generateBtn = document.getElementById('generateBtn');

    // Show status message with longer timeout for better visibility
    function showStatus(message, type) {
      statusDiv.textContent = message;
      statusDiv.className = type;
      setTimeout(() => {
        statusDiv.textContent = '';
        statusDiv.className = '';
      }, 4000); // Adjusted timeout for better visibility
    }

    // Download icon as PNG
    function downloadIcon(canvas, filename) {
      try {
        // Method 1: Using anchor download attribute
        const dataUrl = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();

        // Give browser more time to process
        setTimeout(() => {
          document.body.removeChild(link);
          showStatus(`Download initiated for ${filename}. Check your downloads folder.`, 'success');
        }, 150); // Increased timeout for better reliability
      } catch (error) {
        console.error('Error downloading icon:', error);
        showStatus('Error downloading icon. Try right-clicking the image and "Save As".', 'error');
      }
    }

    document.getElementById('generateBtn').addEventListener('click', function() {
      const iconSizes = [16, 48, 128];
      iconContainer.innerHTML = '';

      // Load the SVG
      const img = new Image();

      img.onload = function() {
        iconSizes.forEach(size => {
          const iconItem = document.createElement('div');
          iconItem.className = 'icon-container';

          // Create size label
          const sizeLabel = document.createElement('p');
          sizeLabel.textContent = `${size}x${size} pixels`;

          // Create canvas and draw image
          const canvas = document.createElement('canvas');
          canvas.width = size;
          canvas.height = size;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, size, size);

          // Create download button
          const downloadBtn = document.createElement('button');
          downloadBtn.textContent = `Download icon${size}.png`;
          downloadBtn.onclick = function() {
            downloadIcon(canvas, `icon${size}.png`);
          };

          // Add manual save instruction
          const manualSave = document.createElement('p');
          manualSave.className = 'manual-save';
          manualSave.textContent = 'If download fails, right-click on image and select "Save Image As..."';

          // Add elements to container
          iconItem.appendChild(sizeLabel);
          iconItem.appendChild(canvas);
          iconItem.appendChild(downloadBtn);
          iconItem.appendChild(manualSave);

          // Add to grid
          iconContainer.appendChild(iconItem);
        });

        showStatus('Icons generated successfully!', 'success');
      };

      img.onerror = function() {
        showStatus('Error loading SVG file. Make sure icon.svg exists in the same folder.', 'error');

        // Try to load from parent directory
        const parentImg = new Image();
        parentImg.onload = function() {
          showStatus('Found SVG in parent directory!', 'success');
          img.src = parentImg.src;
        };
        parentImg.onerror = function() {
          showStatus('Could not find icon.svg in any location. Please add the SVG file.', 'error');
        };
        parentImg.src = '../icon.svg';
      };

      // First try to load from the current folder
      img.src = 'icon.svg';
    });
  </script>
</body>
</html>


