<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 128 128">
  <!-- Background Circle with Google-inspired gradient -->
  <defs>
    <linearGradient id="googleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4285F4" />
      <stop offset="25%" style="stop-color:#34A853" />
      <stop offset="50%" style="stop-color:#FBBC05" />
      <stop offset="75%" style="stop-color:#EA4335" />
      <stop offset="100%" style="stop-color:#4285F4" />
    </linearGradient>
  </defs>

  <!-- Main circle with gradient -->
  <circle cx="64" cy="64" r="60" fill="url(#googleGradient)" />

  <!-- Inner white circle for contrast -->
  <circle cx="64" cy="64" r="52" fill="white" />

  <!-- Audio wave symbol with Google colors -->
  <path d="M40,64 C40,52 45,40 64,40 C83,40 88,52 88,64 C88,76 83,88 64,88 C45,88 40,76 40,64 Z" fill="none" stroke="#4285F4" stroke-width="6" />

  <!-- Left speaker with green color -->
  <path d="M40,64 L30,40 L30,88 L40,64" fill="#34A853" />

  <!-- Right speaker with red color -->
  <path d="M88,64 L98,40 L98,88 L88,64" fill="#EA4335" />

  <!-- Yellow accent in the middle -->
  <circle cx="64" cy="64" r="8" fill="#FBBC05" />
</svg>
