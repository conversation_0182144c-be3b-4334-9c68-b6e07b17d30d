'use strict';

/**
 * reCAPTCHA Audio Solver v3.8.2
 * Refactored for clarity and maintainability
 */

(function () {
  // Early initialization flag to prevent multiple executions
  if (window.recaptchaSolverInitialized) {
    return;
  }
  window.recaptchaSolverInitialized = true;

  // --- Configuration - Using centralized constants with fallbacks ---

  const CONSTANTS = window.RECAPTCHA_CONSTANTS || {
    VERSION_INFO: { VERSION: '3.8.2' },
    ATTEMPT_LIMITS: { MAX_ATTEMPTS_PER_SERVER: 3, MAX_TOTAL_ATTEMPTS: 5 },
    PERFORMANCE_CONFIG: {
      POLLING_INTERVAL_MS: 500,
      CACHE_DURATION_MS: 2000,
      MESSAGE_TIMEOUT_MS: 15000,
      DEBOUNCE_STATUS_UPDATE_MS: 200,
      MUTATION_OBSERVER_DEBOUNCE_MS: 50
    },
    SELECTORS: {
      CHECKBOX: '.recaptcha-checkbox-border',
      AUDIO_BUTTON: '#recaptcha-audio-button',
      IMAGE_BUTTON: '#recaptcha-image-button',
      IMAGE_SELECT: '#rc-imageselect',
      AUDIO_SOURCE: '#audio-source',
      RESPONSE_FIELD: '.rc-audiochallenge-response-field',
      AUDIO_RESPONSE: '#audio-response',
      AUDIO_ERROR_MESSAGE: '.rc-audiochallenge-error-message',
      RELOAD_BUTTON: '#recaptcha-reload-button',
      DOSCAPTCHA: '.rc-doscaptcha-body',
      VERIFY_BUTTON: '#recaptcha-verify-button'
    },
    SERVERS: [
      'https://engageub.pythonanywhere.com',
      'https://engageub1.pythonanywhere.com'
    ]
  };

  const CONFIG = {
    VERSION: `v${CONSTANTS.VERSION_INFO.VERSION}`,
    MAX_ATTEMPTS_PER_SERVER: CONSTANTS.ATTEMPT_LIMITS.MAX_ATTEMPTS_PER_SERVER,
    MAX_TOTAL_ATTEMPTS: CONSTANTS.ATTEMPT_LIMITS.MAX_TOTAL_ATTEMPTS,
    POLLING_INTERVAL_MS: CONSTANTS.PERFORMANCE_CONFIG.POLLING_INTERVAL_MS,
    CACHE_DURATION_MS: CONSTANTS.PERFORMANCE_CONFIG.CACHE_DURATION_MS,
    MESSAGE_TIMEOUT_MS: CONSTANTS.PERFORMANCE_CONFIG.MESSAGE_TIMEOUT_MS,
    DEBOUNCE_STATUS_UPDATE_MS: CONSTANTS.PERFORMANCE_CONFIG.DEBOUNCE_STATUS_UPDATE_MS,
    SELECTORS: CONSTANTS.SELECTORS,
    SERVERS: CONSTANTS.SERVERS,
  };

  // --- State Management ---
  let state = {
    solved: false,
    waiting: false,
    serverIdx: 0,
    serverTry: 0,
    totalAttempts: 0,
    audioUrl: "",
    enabled: true,
    solveCount: 0,
    failCount: 0,
    processingCookies: false,
  };

  const performance = {
    startTime: Date.now(),
    injectionTime: null,
    firstCaptchaDetected: null,
    firstAudioProcessed: null,
    debugMode: false,
    totalProcessingTime: 0,
    successfulAttempts: 0,
    failedAttempts: 0,
    averageProcessingTime: 0
  };

  // --- Helper Functions ---
  /**
   * Enhanced logging function with console and status update
   * @param {string} msg - Message to log
   * @param {boolean} showInConsole - Whether to show in console (default: true)
   */
  const log = (msg, showInConsole = true) => {
    if (showInConsole) {
      console.log(`[reCAPTCHA Audio Solver ${CONFIG.VERSION}] ${msg}`);
    }
    updateStatus(msg);
  };

  /**
   * Debug logging with timestamp for performance analysis
   * @param {string} message - Debug message
   * @param {boolean} force - Force log even if debug mode is off
   */
  const debugLog = (message, force = false) => {
    if (performance.debugMode || force) {
      const timestamp = Date.now() - performance.startTime;
      console.log(`[reCAPTCHA Solver Debug +${timestamp}ms] ${message}`);
    }
  };

  /**
   * Update performance metrics
   * @param {string} type - 'success' or 'failure'
   * @param {number} processingTime - Time taken for processing in ms
   */
  const updatePerformanceMetrics = (type, processingTime = 0) => {
    if (type === 'success') {
      performance.successfulAttempts++;
    } else if (type === 'failure') {
      performance.failedAttempts++;
    }

    if (processingTime > 0) {
      performance.totalProcessingTime += processingTime;
      const totalAttempts = performance.successfulAttempts + performance.failedAttempts;
      performance.averageProcessingTime = totalAttempts > 0 ?
        Math.round(performance.totalProcessingTime / totalAttempts) : 0;
    }

    debugLog(`Performance: ${performance.successfulAttempts} success, ${performance.failedAttempts} failed, avg: ${performance.averageProcessingTime}ms`);
  };

  // Optimized element caching with Map for better memory management
  const elementCache = new Map();
  let cacheTimestamp = 0;
  const $ = (selector) => {
    // Input validation
    if (!selector || typeof selector !== 'string') {
      console.warn('[reCAPTCHA Audio Solver] Invalid selector provided to $()');
      return null;
    }

    const now = Date.now();
    // Clear cache less frequently for better performance
    if (now - cacheTimestamp > CONFIG.CACHE_DURATION_MS) {
      elementCache.clear();
      cacheTimestamp = now;
    }

    if (elementCache.has(selector)) {
      const cached = elementCache.get(selector);
      // More efficient DOM validation - check both connection and parent
      if (cached && cached.isConnected && document.contains(cached)) {
        return cached;
      }
      elementCache.delete(selector);
    }

    try {
      const element = document.querySelector(selector);
      if (element && element.isConnected) {
        elementCache.set(selector, element);
      }
      return element;
    } catch (error) {
      console.warn(`[reCAPTCHA Audio Solver] Invalid selector: ${selector}`, error);
      return null;
    }
  };

  /**
   * Check if element is hidden from view
   * @param {HTMLElement} el - Element to check
   * @returns {boolean} True if element is hidden
   */
  const isHidden = (el) => !el || !el.offsetParent;

  /**
   * Check if processing can continue based on current state
   * @returns {boolean} True if processing is allowed
   */
  const canProcess = () => state.enabled && !state.solved && !state.waiting && !state.processingCookies;

  // --- Forward Declarations for Functions Used in sendMessage ---

  /**
   * Reset state for new CAPTCHA challenge
   */
  function resetStateForNewCaptcha() {
    // Atomic state reset to prevent race conditions
    const newState = {
      solved: false,
      audioUrl: "",
      serverTry: 0,
      serverIdx: 0,
      totalAttempts: 0,
      waiting: false
    };

    // Apply all changes at once
    Object.assign(state, newState);

    debugLog('State reset for new CAPTCHA');
  }

  /**
   * Retry audio processing with fallback logic
   */
  function retryAudio() {
    if (state.solved || state.waiting) return;
    state.waiting = false;

    const fallbackToImage = async (reason) => {
      const imgBtn = $(CONFIG.SELECTORS.IMAGE_BUTTON);
      if (imgBtn) imgBtn.click();
      state.solved = true; // Prevent further processing

      try {
        await updateCounter('fail', reason);
        log(reason, false); // Don't duplicate status update
      } catch (error) {
        console.error('[reCAPTCHA Audio Solver] Failed to update counter on fallback:', error);
        log(reason, false);
      }

      resetStateForNewCaptcha();
    };

    if (state.totalAttempts >= CONFIG.MAX_TOTAL_ATTEMPTS) {
      return fallbackToImage(`Gagal setelah ${CONFIG.MAX_TOTAL_ATTEMPTS} percobaan, beralih ke gambar.`);
    }

    if (state.serverTry < CONFIG.MAX_ATTEMPTS_PER_SERVER) {
      log('Mencoba ulang dengan server yang sama.', false);
      getTextFromAudio(state.audioUrl);
    } else {
      state.serverTry = 0;
      state.serverIdx = (state.serverIdx + 1) % CONFIG.SERVERS.length;
      if (state.serverIdx === 0) {
        return fallbackToImage('Semua server gagal, beralih ke gambar.');
      } else {
        log('Beralih ke server berikutnya.', false);
        getTextFromAudio(state.audioUrl);
      }
    }
  }

  // --- Chrome API Wrappers ---
  let updateStatusTimeout = null;

  /**
   * Centralized counter update function to prevent race conditions
   * @param {string} type - 'solve' or 'fail'
   * @param {string} status - Status message to display
   * @param {number} processingTime - Optional processing time in ms
   */
  function updateCounter(type, status, processingTime = 0) {
    // Input validation
    if (!['solve', 'fail'].includes(type)) {
      console.warn(`[reCAPTCHA Audio Solver] Invalid counter type: ${type}`);
      return Promise.reject(new Error('Invalid counter type'));
    }

    if (!status || typeof status !== 'string') {
      console.warn('[reCAPTCHA Audio Solver] Invalid status provided to updateCounter');
      return Promise.reject(new Error('Invalid status'));
    }

    // Atomic counter update to prevent race conditions
    const previousSolveCount = state.solveCount;
    const previousFailCount = state.failCount;

    if (type === 'solve') {
      state.solveCount++;
      updatePerformanceMetrics('success', processingTime);
    } else if (type === 'fail') {
      state.failCount++;
      updatePerformanceMetrics('failure', processingTime);
    }

    // Calculate success rate with proper validation
    const total = state.solveCount + state.failCount;
    const successRate = total > 0 ? Math.round((state.solveCount / total) * 100) : 0;

    // Update storage and status together to prevent inconsistency
    const data = {
      solveCount: state.solveCount,
      failCount: state.failCount,
      lastStatus: status,
      lastUpdated: Date.now(),
      successRate: successRate
    };

    // Check if Chrome storage is available
    if (!chrome || !chrome.storage || !chrome.storage.local) {
      console.error('[reCAPTCHA Audio Solver] Chrome storage not available');
      // Rollback state changes
      state.solveCount = previousSolveCount;
      state.failCount = previousFailCount;
      return Promise.reject(new Error('Chrome storage not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        chrome.storage.local.set(data, () => {
          if (chrome.runtime.lastError) {
            console.error(`[reCAPTCHA Audio Solver] Storage error: ${chrome.runtime.lastError.message}`);
            // Rollback state changes
            state.solveCount = previousSolveCount;
            state.failCount = previousFailCount;
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          // Send message with error handling
          sendMessage({
            action: 'updateStatus',
            status,
            stats: {
              solved: state.solveCount,
              failed: state.failCount,
              enabled: state.enabled,
              successRate: successRate,
              total: total
            }
          }, (response) => {
            // Add more robust error handling
            if (response && response.success) {
              resolve(data);
            } else {
              console.warn('[reCAPTCHA Audio Solver] Message sending failed, but counter updated');
              // Log more details about the response for debugging
              if (response) {
                console.debug('[reCAPTCHA Audio Solver] Response details:', JSON.stringify(response));
              } else {
                console.debug('[reCAPTCHA Audio Solver] No response received');
              }
              resolve(data); // Still resolve since storage was updated
            }
          });
        });
      } catch (error) {
        console.error(`[reCAPTCHA Audio Solver] Failed to update storage: ${error.message}`);
        // Rollback state changes
        state.solveCount = previousSolveCount;
        state.failCount = previousFailCount;
        reject(error);
      }
    });
  }

  /**
   * Update status without changing counters
   * @param {string} status - Status message to display
   */
  function updateStatus(status) {
    if (!status) return;

    clearTimeout(updateStatusTimeout);
    updateStatusTimeout = setTimeout(() => {
      const data = {
        solveCount: state.solveCount,
        failCount: state.failCount,
        lastStatus: status,
        lastUpdated: Date.now()
      };

      // Check if Chrome APIs are available
      if (!chrome || !chrome.storage || !chrome.storage.local) {
        console.warn('[reCAPTCHA Audio Solver] Chrome storage not available for status update');
        return;
      }

      try {
        chrome.storage.local.set(data, () => {
          if (chrome.runtime.lastError) {
            console.warn(`[reCAPTCHA Audio Solver] Status storage error: ${chrome.runtime.lastError.message}`);
            return;
          }

          // Use the enhanced sendMessage function
          sendMessage({
            action: 'updateStatus',
            status,
            stats: {
              solved: state.solveCount,
              failed: state.failCount,
              enabled: state.enabled
            }
          });
        });
      } catch (error) {
        console.warn(`[reCAPTCHA Audio Solver] Failed to update status: ${error.message}`);
      }
    }, CONFIG.DEBOUNCE_STATUS_UPDATE_MS);
  }

  /**
   * Enhanced message sending with better error handling and retry logic
   * @param {Object} message - Message to send to background script
   * @param {Function} callback - Callback function for response
   */
  function sendMessage(message, callback) {
    // Validate input parameters
    if (!message || typeof message !== 'object') {
      console.error('[reCAPTCHA Audio Solver] Invalid message object provided');
      if (callback && typeof callback === 'function') {
        try {
          callback({ success: false, error: 'Invalid message object' });
        } catch (callbackError) {
          console.error('[reCAPTCHA Audio Solver] Error in callback:', callbackError);
        }
      }
      return;
    }

    // Check if Chrome runtime is available
    if (!chrome || !chrome.runtime || !chrome.runtime.sendMessage) {
      console.error('[reCAPTCHA Audio Solver] Chrome runtime not available');
      if (callback && typeof callback === 'function') {
        try {
          callback({ success: false, error: 'Chrome runtime not available' });
        } catch (callbackError) {
          console.error('[reCAPTCHA Audio Solver] Error in callback:', callbackError);
        }
      }
      return;
    }

    try {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          const errorMsg = chrome.runtime.lastError.message || 'Unknown runtime error';
          console.warn(`[reCAPTCHA Audio Solver] Runtime error: ${errorMsg}`);

          // Handle specific error cases
          if (errorMsg.includes('Extension context invalidated')) {
            console.error('[reCAPTCHA Audio Solver] Extension context invalidated - extension may need reload');
            state.enabled = false; // Disable processing
          }

          if (message.action === 'processAudio') {
            retryAudio();
          }

          // Call callback with error information if provided
          if (callback && typeof callback === 'function') {
            try {
              callback({ success: false, error: errorMsg });
            } catch (callbackError) {
              console.error('[reCAPTCHA Audio Solver] Error in callback:', callbackError);
            }
          }
          return;
        }

        if (callback && typeof callback === 'function') {
          try {
            callback(response);
          } catch (callbackError) {
            console.error('[reCAPTCHA Audio Solver] Error in callback:', callbackError);
          }
        }
      });
    } catch (error) {
      console.error(`[reCAPTCHA Audio Solver] Failed to send message: ${error.message || 'Unknown error'}`);
      if (message.action === 'processAudio') {
        retryAudio();
      }
      // Call callback with error information if provided
      if (callback && typeof callback === 'function') {
        try {
          callback({ success: false, error: error.message || 'Unknown error' });
        } catch (callbackError) {
          console.error('[reCAPTCHA Audio Solver] Error in callback:', callbackError);
        }
      }
    }
  }

  // --- Core Logic ---
  /**
   * Process audio URL to get text transcription
   * @param {string} url - Audio URL to process
   * @returns {void}
   */
  function getTextFromAudio(url) {
    // Enhanced input validation and security checks
    if (!url || typeof url !== 'string') {
      console.error('[reCAPTCHA Audio Solver] Invalid audio URL provided');
      return;
    }

    // Validate URL format and security
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        console.error('[reCAPTCHA Audio Solver] Invalid URL protocol');
        return;
      }
      // Check for valid reCAPTCHA domains
      if (!urlObj.hostname.includes('google.com') && !urlObj.hostname.includes('recaptcha.net')) {
        console.error('[reCAPTCHA Audio Solver] Invalid audio URL domain');
        return;
      }
    } catch (error) {
      console.error('[reCAPTCHA Audio Solver] Malformed audio URL:', error);
      return;
    }

    if (state.waiting || state.solved) return;

    state.waiting = true;
    state.serverTry++;
    state.totalAttempts++;
    const serverUrl = CONFIG.SERVERS[state.serverIdx];
    const processingStartTime = Date.now(); // Track processing time

    // Secure URL processing with validation
    const processedUrl = url.replace(/recaptcha\.net/g, "google.com");

    log(`Mengirim audio ke server ${state.serverIdx + 1}/${CONFIG.SERVERS.length} (Percobaan ${state.serverTry}, Total ${state.totalAttempts})`);

    const messageTimeout = setTimeout(() => {
      if (state.waiting) {
        state.waiting = false;
        const processingTime = Date.now() - processingStartTime;
        log("Timeout saat memproses audio", false);
        // Track timeout failures on final attempt
        if (state.totalAttempts >= CONFIG.MAX_TOTAL_ATTEMPTS) {
          updateCounter('fail', 'Timeout saat memproses audio', processingTime).catch(error => {
            console.error('[reCAPTCHA Audio Solver] Failed to update counter on timeout:', error);
          });
        }
        retryAudio();
      }
    }, CONFIG.MESSAGE_TIMEOUT_MS);

    sendMessage({ action: 'processAudio', audioUrl: processedUrl, serverUrl }, response => {
      clearTimeout(messageTimeout);
      state.waiting = false;
      const processingTime = Date.now() - processingStartTime;

      // Better error handling with specific checks
      if (chrome.runtime.lastError) {
        debugLog(`Chrome runtime error: ${chrome.runtime.lastError.message}`);
        return retryAudio();
      }

      if (state.solved) {
        debugLog("Processing stopped - already solved");
        return;
      }

      if (!response || !response.success || !response.text) {
        debugLog(`Invalid response: ${JSON.stringify(response)}`);
        // Track server response failures
        if (state.totalAttempts >= CONFIG.MAX_TOTAL_ATTEMPTS) {
          // This is the final attempt, count as failure
          updateCounter('fail', 'Server gagal memproses audio setelah beberapa percobaan', processingTime).catch(error => {
            console.error('[reCAPTCHA Audio Solver] Failed to update counter on server failure:', error);
          });
        }
        return retryAudio();
      }

      const { text } = response;
      // Enhanced text validation
      if (!text || typeof text !== 'string' || text === '0' ||
          /<|>/.test(text) || text.length < 2 || text.length > 50 ||
          /^\s*$/.test(text)) {
        debugLog(`Invalid text response: "${text}"`);
        // Track invalid text responses
        if (state.totalAttempts >= CONFIG.MAX_TOTAL_ATTEMPTS) {
          // This is the final attempt, count as failure
          updateCounter('fail', 'Audio tidak dapat ditranskripsi dengan benar', processingTime).catch(error => {
            console.error('[reCAPTCHA Audio Solver] Failed to update counter on invalid text:', error);
          });
        }
        return retryAudio();
      }

      const audioSrcEl = $(CONFIG.SELECTORS.AUDIO_SOURCE);
      const responseEl = $(CONFIG.SELECTORS.AUDIO_RESPONSE);
      const verifyEl = $(CONFIG.SELECTORS.VERIFY_BUTTON);

      // Enhanced element validation
      if (!audioSrcEl || !audioSrcEl.src || audioSrcEl.src !== state.audioUrl) {
        debugLog("Audio source element validation failed");
        return retryAudio();
      }

      if (!responseEl || responseEl.value) {
        debugLog("Response element validation failed");
        return retryAudio();
      }

      if (!verifyEl || verifyEl.disabled) {
        debugLog("Verify button validation failed");
        return retryAudio();
      }

      responseEl.value = text;
      verifyEl.click();

      // Wait for verification to complete before marking as solved
      setTimeout(async () => {
        // Check if CAPTCHA was actually solved by looking for success indicators
        const isStillOnCaptcha = $(CONFIG.SELECTORS.AUDIO_RESPONSE) || $(CONFIG.SELECTORS.CHECKBOX);
        if (!isStillOnCaptcha || !$(CONFIG.SELECTORS.AUDIO_RESPONSE)?.value) {
          // CAPTCHA appears to be solved successfully
          state.solved = true;
          const totalProcessingTime = Date.now() - processingStartTime;
          try {
            await updateCounter('solve', "Tantangan audio berhasil diselesaikan.", totalProcessingTime);
            log("Tantangan audio berhasil diselesaikan.", false);
          } catch (error) {
            console.error('[reCAPTCHA Audio Solver] Failed to update success counter:', error);
            log("Tantangan audio berhasil diselesaikan.", false);
          }
        } else {
          // Verification failed, treat as failure
          debugLog("Verification failed - CAPTCHA still present");
          retryAudio();
        }
      }, 1000); // Wait 1 second for verification to process
    });
  }



  // --- CAPTCHA State Handlers (Refactored from processCaptcha) ---

  function handleDosCaptcha() {
    const dosEl = $(CONFIG.SELECTORS.DOSCAPTCHA);
    if (dosEl && dosEl.innerText) {
      const botMessage = "Bot terdeteksi! reCAPTCHA dihentikan untuk frame ini.";
      log(botMessage, true);

      // Stop processing for this frame permanently
      state.solved = true; // Stop all processing for this frame

      // Update fail counter and set bot detected status
      updateCounter('fail', botMessage);
      chrome.storage.local.set({ botDetected: true });

      // Clean cookies but don't resume processing
      sendMessage({ action: "deleteCookies" }, (response) => {
        if (response && response.success) {
          debugLog(`Berhasil menghapus ${response.count} cookies setelah deteksi bot.`);
        }

        // Only reset status display after delay, but keep processing stopped
        setTimeout(() => {
          const activeMessage = "Aktif dan menunggu reCAPTCHA";
          chrome.storage.local.set({
            botDetected: false,
            lastStatus: activeMessage
          });
          updateStatus(activeMessage);
          // Note: state.solved remains true to prevent processing on this frame
        }, 3000); // 3 second delay before status reset
      });
      return true; // Handled
    }
    return false; // Not handled
  }

  function handleCheckbox() {
    const checkbox = $(CONFIG.SELECTORS.CHECKBOX);
    if (checkbox && !isHidden(checkbox)) {
      if (!performance.firstCaptchaDetected) {
        performance.firstCaptchaDetected = Date.now();
        debugLog(`reCAPTCHA terdeteksi setelah ${performance.firstCaptchaDetected - performance.startTime}ms`, true);
      }
      resetStateForNewCaptcha();
      state.processingCookies = true;
      log("Menghapus cookies sebelum menekan checkbox...", false);
      sendMessage({ action: "deleteCookies" }, (response) => {
        if (response && response.success) {
          debugLog(`Berhasil menghapus ${response.count} cookies.`);
        }
        log("Menekan checkbox...", false);
        checkbox.click();
        state.processingCookies = false;
      });
      return true; // Handled
    }
    return false; // Not handled
  }

  function handleImageChallenge() {
    const audioBtn = $(CONFIG.SELECTORS.AUDIO_BUTTON);
    const imgSel = $(CONFIG.SELECTORS.IMAGE_SELECT);
    if (audioBtn && !isHidden(audioBtn) && imgSel && !isHidden(imgSel)) {
      resetStateForNewCaptcha();
      audioBtn.click();
      log("Beralih ke tantangan audio...", false);
      return true; // Handled
    }
    return false; // Not handled
  }

  function handleAudioChallenge() {
    const audioSrcEl = $(CONFIG.SELECTORS.AUDIO_SOURCE);
    if (!audioSrcEl || !audioSrcEl.src) return false;

    const reloadBtn = $(CONFIG.SELECTORS.RELOAD_BUTTON);
    const errorMsgEl = $(CONFIG.SELECTORS.AUDIO_ERROR_MESSAGE);
    const needsReload = (errorMsgEl && errorMsgEl.innerText && reloadBtn && !reloadBtn.disabled) ||
                        (!state.waiting && state.audioUrl === audioSrcEl.src && reloadBtn);

    if (needsReload && reloadBtn && !reloadBtn.disabled) {
      reloadBtn.click();
      log("Memuat ulang audio.", false);
      state.audioUrl = "";
      return true; // Handled
    }

    const responseField = $(CONFIG.SELECTORS.RESPONSE_FIELD);
    const responseInput = $(CONFIG.SELECTORS.AUDIO_RESPONSE);
    const shouldProcess = !state.waiting && !state.solved &&
                          responseField && !isHidden(responseField) &&
                          (!responseInput || !responseInput.value) &&
                          state.audioUrl !== audioSrcEl.src;

    if (shouldProcess) {
      if (!performance.firstAudioProcessed) {
        performance.firstAudioProcessed = Date.now();
        debugLog(`Memproses audio pertama setelah ${performance.firstAudioProcessed - performance.startTime}ms`, true);
      }
      state.audioUrl = audioSrcEl.src;
      debugLog(`Audio baru terdeteksi: ${state.audioUrl.substring(0, 50)}...`);
      getTextFromAudio(state.audioUrl);
      return true; // Handled
    }
    return false; // Not handled
  }

  function processCaptcha() {
    if (!canProcess()) return;

    if (handleDosCaptcha()) return;
    if (handleCheckbox()) return;
    if (handleImageChallenge()) return;
    if (handleAudioChallenge()) return;
  }

  // --- Initialization and Listeners ---
  let observer = null;
  let observerTimeout = null;

  /**
   * Optimized MutationObserver setup with debouncing for better performance
   */
  function setupMutationObserver() {
    if (observer) return;

    observer = new MutationObserver((mutations) => {
      // Debounce mutations to prevent excessive processing
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // Only process if we have relevant mutations
      const hasRelevantMutation = mutations.some(mutation =>
        mutation.type === 'childList' ||
        (mutation.type === 'attributes' &&
         ['src', 'style', 'class'].includes(mutation.attributeName))
      );

      if (hasRelevantMutation && canProcess()) {
        observerTimeout = setTimeout(processCaptcha, CONSTANTS.PERFORMANCE_CONFIG.MUTATION_OBSERVER_DEBOUNCE_MS);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['src', 'style', 'class']
    });
    debugLog("Optimized MutationObserver setup completed");
  }

  function disconnectMutationObserver() {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
    if (observerTimeout) {
      clearTimeout(observerTimeout);
      observerTimeout = null;
    }
    debugLog("MutationObserver and timeouts cleaned up");
  }

  // Store interval ID for proper cleanup
  let pollingInterval = null;

  function startSolver() {
    // Clear any existing interval to prevent duplicates
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }
    // The observer is primary, the interval is a fallback
    pollingInterval = setInterval(processCaptcha, CONFIG.POLLING_INTERVAL_MS);
  }

  function stopSolver() {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      pollingInterval = null;
    }
  }

  // Enhanced message listener with better error handling
  if (chrome && chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
      try {
        if (!request || !request.action) {
          sendResponse({ success: false, error: 'Invalid request' });
          return false;
        }

        switch (request.action) {
          case "toggleSolver":
            if (typeof request.enabled !== 'boolean') {
              sendResponse({ success: false, error: 'Invalid enabled value' });
              return true;
            }

            state.enabled = request.enabled;

            // Safe storage update
            if (chrome && chrome.storage && chrome.storage.local) {
              chrome.storage.local.set({ enabled: state.enabled }, () => {
                if (chrome.runtime.lastError) {
                  console.warn(`[reCAPTCHA Audio Solver] Storage error: ${chrome.runtime.lastError.message}`);
                }
              });
            }

            if (state.enabled) {
              setupMutationObserver();
              startSolver();
            } else {
              disconnectMutationObserver();
              stopSolver();
            }
            log(state.enabled ? "Solver diaktifkan" : "Solver dinonaktifkan");
            sendResponse({ success: true, solverRunning: state.enabled });
            return true;

          case "getStats":
            sendResponse({
              solved: state.solveCount,
              failed: state.failCount,
              enabled: state.enabled,
              status: state.waiting ? 'memproses' : (state.solved ? 'berhasil' : 'menunggu'),
              captchaDetected: !!($(CONFIG.SELECTORS.CHECKBOX) || $(CONFIG.SELECTORS.AUDIO_BUTTON)),
              performance: { ...performance, startTime: undefined }
            });
            return true;

          case "toggleDebug":
            if (typeof request.enabled !== 'boolean') {
              sendResponse({ success: false, error: 'Invalid enabled value' });
              return true;
            }

            performance.debugMode = request.enabled;
            debugLog(`Mode debug ${performance.debugMode ? 'diaktifkan' : 'dinonaktifkan'}`, true);
            sendResponse({ success: true, debugMode: performance.debugMode });
            return true;

          case "statsUpdated":
            // Handle real-time stats updates from background script
            if (request.stats) {
              // Update local state to keep in sync
              state.solveCount = request.stats.solveCount || 0;
              state.failCount = request.stats.failCount || 0;
              debugLog(`Stats updated: ${state.solveCount} solved, ${state.failCount} failed`);
            }
            sendResponse({ success: true });
            return true;

          default:
            console.warn(`[reCAPTCHA Audio Solver] Unknown message action: ${request.action}`);
            sendResponse({ success: false, error: 'Unknown action' });
            return true;
        }
      } catch (error) {
        console.error(`[reCAPTCHA Audio Solver] Message listener error: ${error.message}`);
        sendResponse({ success: false, error: error.message });
        return true;
      }
    });
  } else {
    console.warn('[reCAPTCHA Audio Solver] Chrome runtime message API not available');
  }

  // --- Initial Load ---
  function initializeExtension() {
    // Check if Chrome APIs are available
    if (!chrome || !chrome.storage || !chrome.storage.local) {
      console.warn('[reCAPTCHA Audio Solver] Chrome APIs not available, using defaults');
      state.solveCount = 0;
      state.failCount = 0;
      state.enabled = true;
      log("Aktif (default - no storage)", false);
      setupMutationObserver();
      startSolver();
      return;
    }

    try {
      chrome.storage.local.get(['solveCount', 'failCount', 'enabled'], (result) => {
        if (chrome.runtime.lastError) {
          console.error(`[reCAPTCHA Audio Solver] Storage error during initialization: ${chrome.runtime.lastError.message}`);
          // Use default values if storage fails
          state.solveCount = 0;
          state.failCount = 0;
          state.enabled = true;
        } else {
          // Validate and sanitize stored values
          state.solveCount = Math.max(0, parseInt(result.solveCount) || 0);
          state.failCount = Math.max(0, parseInt(result.failCount) || 0);
          if (typeof result.enabled === 'boolean') {
            state.enabled = result.enabled;
          }
        }

        log(state.enabled ? "Aktif" : "Nonaktif", false);
        if (state.enabled) {
          setupMutationObserver();
          startSolver();
        }
      });
    } catch (error) {
      console.error(`[reCAPTCHA Audio Solver] Failed to initialize from storage: ${error.message}`);
      // Use default values if initialization fails
      state.solveCount = 0;
      state.failCount = 0;
      state.enabled = true;
      log("Aktif (default)", false);
      setupMutationObserver();
      startSolver();
    }
  }

  // Initialize the extension
  initializeExtension();

  performance.injectionTime = Date.now();
  debugLog(`Skrip konten dimuat dalam ${performance.injectionTime - performance.startTime}ms`, true);

  // --- Cleanup Function ---
  function cleanup() {
    debugLog('Cleaning up reCAPTCHA Audio Solver resources');

    // Stop all timers and intervals
    stopSolver();
    disconnectMutationObserver();

    // Clear timeouts
    if (updateStatusTimeout) {
      clearTimeout(updateStatusTimeout);
      updateStatusTimeout = null;
    }

    // Clear element cache
    elementCache.clear();

    // Reset state
    state.solved = true; // Prevent further processing
    state.waiting = false;

    debugLog('Cleanup completed');
  }

  // Register cleanup for page unload
  window.addEventListener('beforeunload', cleanup);
  window.addEventListener('unload', cleanup);

})();
